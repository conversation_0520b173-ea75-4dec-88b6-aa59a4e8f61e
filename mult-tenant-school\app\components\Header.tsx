import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaComment } from 'react-icons/fa';

const Header = () => {
  return (
    <div className="flex justify-between items-center p-6 glass-header">
      <div>
        <h1 className="text-2xl font-bold text-text-primary text-shadow-glass">Welcome.</h1>
        <p className="text-text-secondary">Navigate the future of education with Schoooli.</p>
      </div>
      <div className="flex items-center gap-6">
        <div className="relative">
          <input
            type="text"
            placeholder="Search anything here"
            className="glass-input w-80 py-3 pl-10 pr-4"
          />
          <FaSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-text-muted" />
        </div>
        <div className="flex items-center gap-4">
            <button className="glass-button p-3 rounded-xl">
                <FaBell className="text-xl text-text-secondary" />
            </button>
            <button className="glass-button p-3 rounded-xl">
                <FaComment className="text-xl text-text-secondary" />
            </button>
        </div>
        <div className="flex items-center gap-3 glass-card p-3 rounded-xl">
          <div className="relative">
            <img
              src="https://randomuser.me/api/portraits/men/32.jpg"
              alt="User"
              className="w-11 h-11 rounded-full border-2 border-accent-purple/30"
            />
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-accent-green rounded-full border-2 border-white"></div>
          </div>
          <div>
            <p className="font-semibold text-text-primary">Luke F R</p>
            <p className="text-sm text-text-secondary">Admin</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
