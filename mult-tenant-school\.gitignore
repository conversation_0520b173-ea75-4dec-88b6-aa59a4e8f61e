# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# backend dependencies
backend/node_modules/

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
backend/.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# prisma
/src/generated/prisma
backend/prisma/migrations/

# database files (development only)
backend/dev.db
backend/dev.db-journal

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
Thumbs.db
