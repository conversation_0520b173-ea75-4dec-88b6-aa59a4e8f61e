# School Management System Product Requirements Document (PRD)

## Table of Contents

- [School Management System Product Requirements Document (PRD)](#table-of-contents)
  - [1. Goals and Background Context](./1-goals-and-background-context.md)
    - [1.1. Goals](./1-goals-and-background-context.md#11-goals)
    - [1.2. Background Context](./1-goals-and-background-context.md#12-background-context)
    - [1.3. Change Log](./1-goals-and-background-context.md#13-change-log)
  - [2. Requirements](./2-requirements.md)
    - [2.1. Functional](./2-requirements.md#21-functional)
      - [2.1.1 User & Role Management](./2-requirements.md#211-user-role-management)
      - [2.1.2 Academic Management](./2-requirements.md#212-academic-management)
      - [2.1.3 Student & Teacher Management](./2-requirements.md#213-student-teacher-management)
      - [2.1.4 Examination & Grading](./2-requirements.md#214-examination-grading)
      - [2.1.5 Attendance Management](./2-requirements.md#215-attendance-management)
      - [2.1.6 Admin Portal & Dashboard](./2-requirements.md#216-admin-portal-dashboard)
    - [2.2. Non Functional](./2-requirements.md#22-non-functional)
  - [3. User Interface Design Goals](./3-user-interface-design-goals.md)
    - [3.1. Overall UX Vision](./3-user-interface-design-goals.md#31-overall-ux-vision)
    - [3.2. Key Interaction Paradigms](./3-user-interface-design-goals.md#32-key-interaction-paradigms)
    - [3.3. Core Screens and Views](./3-user-interface-design-goals.md#33-core-screens-and-views)
    - [3.4. Accessibility: WCAG 2.1 AA](./3-user-interface-design-goals.md#34-accessibility-wcag-21-aa)
    - [3.5. Branding](./3-user-interface-design-goals.md#35-branding)
    - [3.6. Target Device and Platforms: Web Responsive (Desktop and Mobile Browsers)](./3-user-interface-design-goals.md#36-target-device-and-platforms-web-responsive-desktop-and-mobile-browsers)
  - [4. Technical Assumptions](./4-technical-assumptions.md)
    - [4.1. Repository Structure: Polyrepo (Frontend, Backend, and potentially separate services)](./4-technical-assumptions.md#41-repository-structure-polyrepo-frontend-backend-and-potentially-separate-services)
    - [4.2. Service Architecture](./4-technical-assumptions.md#42-service-architecture)
    - [4.3. Testing Requirements](./4-technical-assumptions.md#43-testing-requirements)
    - [4.4. Additional Technical Assumptions and Requests](./4-technical-assumptions.md#44-additional-technical-assumptions-and-requests)
  - [5. Epic List](./5-epic-list.md)
  - [6. Checklist Results Report](./6-checklist-results-report.md)
    - [6. Next Steps](./6-checklist-results-report.md#6-next-steps)
    - [7. Hindsight is 20/20: The 'If Only...' Reflection](./6-checklist-results-report.md#7-hindsight-is-2020-the-if-only-reflection)
      - [7.1. Key Insights and 'If Only...' Scenarios](./6-checklist-results-report.md#71-key-insights-and-if-only-scenarios)
      - [7.2. Proactive Measures and Adjustments](./6-checklist-results-report.md#72-proactive-measures-and-adjustments)
