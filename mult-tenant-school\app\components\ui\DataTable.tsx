import React, { useState } from 'react';
import { FaSort, FaSortUp, FaSortDown, FaSearch, FaFilter } from 'react-icons/fa';

interface Column {
  key?: string;
  accessorKey?: string;
  header: string;
  label?: string;
  sortable?: boolean;
  render?: (value: any, row: any, rowIndex?: number) => React.ReactNode;
  cell?: ({ row }: { row: { original: any } }) => React.ReactNode;
  id?: string;
}

interface DataTableProps {
  data: any[];
  columns: Column[];
  searchable?: boolean;
  filterable?: boolean;
  pagination?: boolean;
  pageSize?: number;
  className?: string;
  loading?: boolean;
}

const DataTable = ({
  data,
  columns,
  searchable = true,
  filterable = true,
  pagination = true,
  pageSize = 10,
  className = '',
  loading = false,
}: DataTableProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);
  const [currentPage, setCurrentPage] = useState(1);

  // Filter data based on search term
  const filteredData = (data || []).filter((row) =>
    row && Object.values(row).some((value) =>
      value != null && String(value).toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  // Sort data
  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortConfig || !a || !b) return 0;

    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];

    if (aValue < bValue) {
      return sortConfig.direction === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortConfig.direction === 'asc' ? 1 : -1;
    }
    return 0;
  });

  // Paginate data
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedData = pagination ? sortedData.slice(startIndex, endIndex) : sortedData;
  const totalPages = Math.ceil(sortedData.length / pageSize);

  const handleSort = (key: string) => {
    setSortConfig((current) => {
      if (current?.key === key) {
        return {
          key,
          direction: current.direction === 'asc' ? 'desc' : 'asc',
        };
      }
      return { key, direction: 'asc' };
    });
  };

  const getColumnKey = (column: Column) => {
    return column.accessorKey || column.key || column.id || '';
  };

  const getColumnHeader = (column: Column) => {
    return column.header || column.label || '';
  };

  const getSortIcon = (key: string) => {
    if (sortConfig?.key !== key) return <FaSort className="text-gray-400" />;
    return sortConfig.direction === 'asc' ? (
      <FaSortUp className="text-accent-purple" />
    ) : (
      <FaSortDown className="text-accent-purple" />
    );
  };

  return (
    <div className={`glass-card p-6 ${className}`}>
      {/* Search and Filter Bar */}
      {(searchable || filterable) && (
        <div className="flex items-center gap-4 mb-6">
          {searchable && (
            <div className="relative flex-1 max-w-md">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-muted" />
              <input
                type="text"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="glass-input w-full pl-10 pr-4 py-2"
              />
            </div>
          )}
          {filterable && (
            <button className="glass-button px-4 py-2 flex items-center gap-2">
              <FaFilter className="text-sm" />
              Filter
            </button>
          )}
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-glass-border">
              {columns.map((column, index) => {
                const columnKey = getColumnKey(column);
                const columnHeader = getColumnHeader(column);
                return (
                  <th
                    key={columnKey || index}
                    className={`text-left py-3 px-4 font-semibold text-text-primary ${
                      column.sortable ? 'cursor-pointer hover:text-accent-purple' : ''
                    }`}
                    onClick={() => column.sortable && columnKey && handleSort(columnKey)}
                  >
                    <div className="flex items-center gap-2">
                      {columnHeader}
                      {column.sortable && columnKey && getSortIcon(columnKey)}
                    </div>
                  </th>
                );
              })}
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={columns.length} className="py-8 px-4 text-center text-text-secondary">
                  Loading...
                </td>
              </tr>
            ) : paginatedData.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="py-8 px-4 text-center text-text-secondary">
                  No data available
                </td>
              </tr>
            ) : (
              paginatedData.map((row, index) => (
                <tr
                  key={index}
                  className="border-b border-glass-border hover:bg-glass-white/50 transition-colors duration-200"
                >
                  {columns.map((column, colIndex) => {
                    const columnKey = getColumnKey(column);
                    return (
                      <td key={columnKey || colIndex} className="py-4 px-4 text-text-secondary">
                        {column.cell
                          ? column.cell({ row: { original: row } })
                          : column.render
                          ? column.render(row?.[columnKey], row, index)
                          : row?.[columnKey] || '-'}
                      </td>
                    );
                  })}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && totalPages > 1 && (
        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-text-secondary">
            Showing {startIndex + 1} to {Math.min(endIndex, sortedData.length)} of{' '}
            {sortedData.length} results
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="glass-button px-3 py-1 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`px-3 py-1 rounded-lg transition-all duration-200 ${
                  page === currentPage
                    ? 'bg-accent-purple text-white shadow-purple-glow'
                    : 'glass-button'
                }`}
              >
                {page}
              </button>
            ))}
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="glass-button px-3 py-1 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataTable;
