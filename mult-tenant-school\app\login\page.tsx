'use client';

import { useState } from 'react';
import { FaSignInAlt, FaEye, FaEyeSlash, FaUser, FaLock, FaUserShield, FaGraduationCap, FaChalkboardTeacher, FaBuilding } from 'react-icons/fa';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { notificationService } from '@/lib/notifications';
import { authService } from '@/lib/auth';

export default function LoginPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      
      // Client-side validation
      if (!formData.email.trim()) {
        notificationService.error('Email is required');
        return;
      }
      
      if (!formData.password.trim()) {
        notificationService.error('Password is required');
        return;
      }
      
      if (!formData.email.includes('@')) {
        notificationService.error('Please enter a valid email address');
        return;
      }
      
      notificationService.info('Logging in...');
      
      // API call to login
      const result = await authService.login({
        email: formData.email,
        password: formData.password,
        rememberMe: formData.rememberMe
      });
      
      notificationService.success('Login successful!');
      
      // Redirect based on user role
      const user = result.user;
      if (user && user.roles) {
        const roleNames = user.roles.map(role => role.name);
        
        if (roleNames.includes('Super Admin')) {
          // Super Admin goes to system dashboard
          window.location.href = '/';
        } else if (roleNames.includes('Tenant Admin')) {
          // Tenant Admin goes to school dashboard
          window.location.href = '/';
        } else if (roleNames.includes('Teacher')) {
          // Teacher goes to teacher dashboard
          window.location.href = '/';
        } else if (roleNames.includes('Student')) {
          // Student goes to student dashboard
          window.location.href = '/';
        } else {
          // Default redirect
          window.location.href = '/';
        }
      } else {
        // Fallback redirect
        window.location.href = '/';
      }
      
    } catch (err) {
      console.error('Login error:', err);
      notificationService.error(err instanceof Error ? err.message : 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4">
            <FaSignInAlt className="text-white text-2xl" />
          </div>
          <h1 className="text-3xl font-bold text-text-primary">Welcome Back</h1>
          <p className="text-text-secondary mt-2">Sign in to your account</p>
        </div>

        {/* Login Form */}
        <Card>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Email Address
              </label>
              <div className="relative">
                <FaUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" />
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="glass-input w-full pl-10"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Password
              </label>
              <div className="relative">
                <FaLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" />
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  className="glass-input w-full pl-10 pr-10"
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text-secondary hover:text-text-primary transition-colors"
                >
                  {showPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
            </div>

            {/* Remember Me */}
            <div className="flex items-center justify-between">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="rememberMe"
                  checked={formData.rememberMe}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-text-secondary">Remember me</span>
              </label>
              <a href="#" className="text-sm text-blue-600 hover:text-blue-500">
                Forgot password?
              </a>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              variant="primary"
              disabled={loading}
              className="w-full flex items-center justify-center space-x-2"
            >
              <FaSignInAlt />
              <span>{loading ? 'Signing in...' : 'Sign In'}</span>
            </Button>
          </form>
        </Card>

        {/* Role Information */}
        <Card variant="gradient" glow="blue">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">User Roles & Access</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center p-3 bg-white rounded-lg">
              <FaUserShield className="text-purple-500 mr-3 text-xl" />
              <div>
                <h4 className="font-medium text-gray-900">Super Admin</h4>
                <p className="text-sm text-gray-600">System-wide management</p>
              </div>
            </div>
            <div className="flex items-center p-3 bg-white rounded-lg">
              <FaBuilding className="text-blue-500 mr-3 text-xl" />
              <div>
                <h4 className="font-medium text-gray-900">Tenant Admin</h4>
                <p className="text-sm text-gray-600">School management</p>
              </div>
            </div>
            <div className="flex items-center p-3 bg-white rounded-lg">
              <FaChalkboardTeacher className="text-green-500 mr-3 text-xl" />
              <div>
                <h4 className="font-medium text-gray-900">Teacher</h4>
                <p className="text-sm text-gray-600">Academic functions</p>
              </div>
            </div>
            <div className="flex items-center p-3 bg-white rounded-lg">
              <FaGraduationCap className="text-orange-500 mr-3 text-xl" />
              <div>
                <h4 className="font-medium text-gray-900">Student</h4>
                <p className="text-sm text-gray-600">Student portal access</p>
              </div>
            </div>
          </div>
        </Card>

        {/* Footer */}
        <div className="text-center mt-6">
          <p className="text-text-secondary">
            Don't have an account?{' '}
            <a href="/register" className="text-blue-600 hover:text-blue-500 font-medium">
              Sign up here
            </a>
          </p>
        </div>

        {/* Demo Credentials */}
        <Card className="mt-6 bg-blue-50 border-blue-200">
          <div className="p-4">
            <h3 className="text-sm font-medium text-blue-800 mb-2">Demo Credentials</h3>
            <div className="text-xs text-blue-700 space-y-1">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Password:</strong> admin123</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
