'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  ArrowLeft, 
  ArrowRight,
  Save, 
  User, 
  Phone, 
  MapPin, 
  GraduationCap,
  Heart,
  AlertCircle,
  Mail,
  Calendar,
  Shield,
  FileText,
  Users,
  Home,
  BookOpen,
  CheckCircle,
  Clock,
  Star,
  Sparkles
} from 'lucide-react';
import { studentService } from '@/lib/studentService';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
}

interface FormData {
  // Personal Information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  studentId: string;
  admissionNumber: string;
  dateOfBirth: string;
  gender: 'MALE' | 'FEMALE' | 'OTHER' | '';
  nationality: string;
  religion: string;
  bloodGroup: string;
  
  // Address Information
  address: string;
  city: string;
  region: string;
  postalCode: string;
  
  // Emergency Contact
  emergencyContact: string;
  emergencyPhone: string;
  emergencyRelation: string;
  
  // Academic Information
  admissionDate: string;
  previousSchool: string;
  previousGrade: string;
  
  // Additional Information
  medicalInfo: string;
  transportMode: string;
  transportRoute: string;
  specialNeeds: string;
  hobbies: string;
}

const steps = [
  {
    id: 1,
    title: 'Personal Details',
    description: 'Basic student information',
    icon: User,
    color: 'from-accent-purple to-accent-purple-light'
  },
  {
    id: 2,
    title: 'Contact & Address',
    description: 'Location and contact details',
    icon: Home,
    color: 'from-accent-blue to-accent-blue-light'
  },
  {
    id: 3,
    title: 'Emergency Contact',
    description: 'Emergency contact information',
    icon: Shield,
    color: 'from-accent-green to-accent-green-light'
  },
  {
    id: 4,
    title: 'Academic Background',
    description: 'Previous education details',
    icon: BookOpen,
    color: 'from-status-warning to-yellow-400'
  },
  {
    id: 5,
    title: 'Additional Info',
    description: 'Medical and other details',
    icon: Heart,
    color: 'from-status-danger to-red-400'
  }
];

export default function NewStudentPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [users, setUsers] = useState<User[]>([]);
  const [formData, setFormData] = useState<FormData>({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    studentId: '',
    admissionNumber: '',
    dateOfBirth: '',
    gender: '',
    nationality: 'Tanzanian',
    religion: '',
    bloodGroup: '',
    
    // Address Information
    address: '',
    city: '',
    region: '',
    postalCode: '',
    
    // Emergency Contact
    emergencyContact: '',
    emergencyPhone: '',
    emergencyRelation: '',
    
    // Academic Information
    admissionDate: '',
    previousSchool: '',
    previousGrade: '',
    
    // Additional Information
    medicalInfo: '',
    transportMode: '',
    transportRoute: '',
    specialNeeds: '',
    hobbies: ''
  });

  useEffect(() => {
    loadUsers();
    // Generate a default student ID if empty
    if (!formData.studentId) {
      const timestamp = Date.now().toString().slice(-6);
      setFormData(prev => ({
        ...prev,
        studentId: `STU${timestamp}`
      }));
    }
  }, []);

  const loadUsers = async () => {
    try {
      // This would typically load users who don't have student profiles yet
      // For now, we'll use a placeholder
      setUsers([]);
    } catch (error) {
      console.error('Error loading users:', error);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.firstName && formData.lastName && formData.email && 
                 formData.studentId && formData.dateOfBirth && formData.gender);
      case 2:
        return !!(formData.address && formData.city && formData.region);
      case 3:
        return !!(formData.emergencyContact && formData.emergencyPhone && formData.emergencyRelation);
      case 4:
        return !!(formData.admissionDate);
      case 5:
        return true; // Optional information
      default:
        return false;
    }
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      if (currentStep < steps.length) {
        setCurrentStep(currentStep + 1);
      }
    } else {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields before proceeding',
        variant: 'destructive'
      });
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate all required fields
    const requiredFields = ['firstName', 'lastName', 'email', 'studentId', 'dateOfBirth', 'gender'];
    const missingFields = requiredFields.filter(field => !formData[field as keyof FormData]);
    
    if (missingFields.length > 0) {
      toast({
        title: 'Validation Error',
        description: `Missing required fields: ${missingFields.join(', ')}`,
        variant: 'destructive'
      });
      return;
    }

    try {
      setLoading(true);
      
      // Create the user first (in a real app, this might be a separate step)
      const userData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone
      };

      // Then create the student with all the form data
      const studentData = {
        ...formData,
        gender: formData.gender as 'MALE' | 'FEMALE' | 'OTHER'
      };

      await studentService.createStudent(studentData);
      
      toast({
        title: 'Success! 🎉',
        description: 'Student registered successfully',
      });
      
      router.push('/students');
    } catch (error: any) {
      console.error('Error creating student:', error);
      
      let errorMessage = 'Failed to create student';
      
      if (error.message) {
        if (error.message.includes('email already exists')) {
          errorMessage = 'A user with this email already exists. Please use a different email address.';
        } else if (error.message.includes('Student with this ID already exists')) {
          errorMessage = 'A student with this ID already exists. Please use a different student ID.';
        } else {
          errorMessage = error.message;
        }
      }
      
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
      <div className="space-y-6">
        {/* Header - Match system style */}
        <div className="glass-card p-6 bg-gradient-to-r from-accent-purple/10 to-accent-blue/10 border-accent-purple/30">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-4 mb-2">
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => router.push('/students')}
                  className="flex items-center"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Students
                </Button>
              </div>
              <h1 className="text-3xl font-bold text-text-primary">Add New Student</h1>
              <p className="text-text-secondary">
                Create a new student profile with personal and academic information
              </p>
            </div>
            <div className="p-3 rounded-xl bg-gradient-to-r from-accent-purple to-accent-purple-light shadow-purple-glow">
              <GraduationCap className="h-8 w-8 text-white" />
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Personal Information */}
            <Card>
              <div className="p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="p-2 rounded-lg bg-blue-100">
                    <User className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Personal Information</h3>
                    <p className="text-sm text-gray-600">Basic personal details of the student</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">First Name *</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => handleInputChange('firstName', e.target.value)}
                        placeholder="e.g., John"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName">Last Name *</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => handleInputChange('lastName', e.target.value)}
                        placeholder="e.g., Doe"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="e.g., <EMAIL>"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="studentId">Student ID *</Label>
                      <Input
                        id="studentId"
                        value={formData.studentId}
                        onChange={(e) => handleInputChange('studentId', e.target.value)}
                        placeholder="e.g., STU001"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="admissionNumber">Admission Number</Label>
                      <Input
                        id="admissionNumber"
                        value={formData.admissionNumber}
                        onChange={(e) => handleInputChange('admissionNumber', e.target.value)}
                        placeholder="e.g., ADM2024001"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="dateOfBirth">Date of Birth *</Label>
                      <Input
                        id="dateOfBirth"
                        type="date"
                        value={formData.dateOfBirth}
                        onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="admissionDate">Admission Date</Label>
                      <Input
                        id="admissionDate"
                        type="date"
                        value={formData.admissionDate}
                        onChange={(e) => handleInputChange('admissionDate', e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="gender">Gender *</Label>
                      <select
                        value={formData.gender}
                        onChange={(e) => handleInputChange('gender', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Select gender</option>
                        <option value="MALE">Male</option>
                        <option value="FEMALE">Female</option>
                        <option value="OTHER">Other</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="nationality">Nationality</Label>
                      <Input
                        id="nationality"
                        value={formData.nationality}
                        onChange={(e) => handleInputChange('nationality', e.target.value)}
                        placeholder="e.g., Tanzanian"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="religion">Religion</Label>
                      <Input
                        id="religion"
                        value={formData.religion}
                        onChange={(e) => handleInputChange('religion', e.target.value)}
                        placeholder="e.g., Christian, Muslim"
                      />
                    </div>
                    <div>
                      <Label htmlFor="bloodGroup">Blood Group</Label>
                      <select
                        value={formData.bloodGroup}
                        onChange={(e) => handleInputChange('bloodGroup', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Select blood group</option>
                        <option value="A+">A+</option>
                        <option value="A-">A-</option>
                        <option value="B+">B+</option>
                        <option value="B-">B-</option>
                        <option value="AB+">AB+</option>
                        <option value="AB-">AB-</option>
                        <option value="O+">O+</option>
                        <option value="O-">O-</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Contact Information */}
            <Card>
              <div className="p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="p-2 rounded-lg bg-green-100">
                    <Phone className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Contact Information</h3>
                    <p className="text-sm text-gray-600">Address and contact details</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="address">Address *</Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      placeholder="Street address"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="city">City *</Label>
                      <Input
                        id="city"
                        value={formData.city}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        placeholder="e.g., Dar es Salaam"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="region">Region *</Label>
                      <Input
                        id="region"
                        value={formData.region}
                        onChange={(e) => handleInputChange('region', e.target.value)}
                        placeholder="e.g., Dar es Salaam"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="postalCode">Postal Code</Label>
                      <Input
                        id="postalCode"
                        value={formData.postalCode}
                        onChange={(e) => handleInputChange('postalCode', e.target.value)}
                        placeholder="e.g., 11101"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="e.g., +255 123 456 789"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Emergency Contact */}
            <Card>
              <div className="p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="p-2 rounded-lg bg-red-100">
                    <AlertCircle className="h-5 w-5 text-red-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Emergency Contact</h3>
                    <p className="text-sm text-gray-600">Emergency contact information</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="emergencyContact">Emergency Contact Name *</Label>
                    <Input
                      id="emergencyContact"
                      value={formData.emergencyContact}
                      onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
                      placeholder="e.g., John Doe"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="emergencyPhone">Emergency Phone Number *</Label>
                    <Input
                      id="emergencyPhone"
                      value={formData.emergencyPhone}
                      onChange={(e) => handleInputChange('emergencyPhone', e.target.value)}
                      placeholder="e.g., +255 123 456 789"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="emergencyRelation">Relationship</Label>
                    <Input
                      id="emergencyRelation"
                      value={formData.emergencyRelation}
                      onChange={(e) => handleInputChange('emergencyRelation', e.target.value)}
                      placeholder="e.g., Parent, Guardian"
                    />
                  </div>
                </div>
              </div>
            </Card>

            {/* Academic Information */}
            <Card>
              <div className="p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="p-2 rounded-lg bg-purple-100">
                    <GraduationCap className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Academic Information</h3>
                    <p className="text-sm text-gray-600">Previous academic background</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="previousSchool">Previous School</Label>
                    <Input
                      id="previousSchool"
                      value={formData.previousSchool}
                      onChange={(e) => handleInputChange('previousSchool', e.target.value)}
                      placeholder="e.g., ABC Primary School"
                    />
                  </div>
                  <div>
                    <Label htmlFor="previousGrade">Previous Grade/Class</Label>
                    <Input
                      id="previousGrade"
                      value={formData.previousGrade}
                      onChange={(e) => handleInputChange('previousGrade', e.target.value)}
                      placeholder="e.g., Grade 6"
                    />
                  </div>
                </div>
              </div>
            </Card>

            {/* Transport Information */}
            <Card>
              <div className="p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="p-2 rounded-lg bg-orange-100">
                    <MapPin className="h-5 w-5 text-orange-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Transport Information</h3>
                    <p className="text-sm text-gray-600">Transportation details</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="transportMode">Transport Mode</Label>
                    <select
                      value={formData.transportMode}
                      onChange={(e) => handleInputChange('transportMode', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Select transport mode</option>
                      <option value="BUS">School Bus</option>
                      <option value="WALKING">Walking</option>
                      <option value="PRIVATE">Private Vehicle</option>
                      <option value="OTHER">Other</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="transportRoute">Transport Route</Label>
                    <Input
                      id="transportRoute"
                      value={formData.transportRoute}
                      onChange={(e) => handleInputChange('transportRoute', e.target.value)}
                      placeholder="e.g., Route A, Kimara"
                    />
                  </div>
                </div>
              </div>
            </Card>

            {/* Medical Information */}
            <Card>
              <div className="p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="p-2 rounded-lg bg-pink-100">
                    <Heart className="h-5 w-5 text-pink-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Medical Information</h3>
                    <p className="text-sm text-gray-600">Health conditions and medical notes</p>
                  </div>
                </div>
                <div>
                  <Label htmlFor="medicalInfo">Medical Information</Label>
                  <Textarea
                    id="medicalInfo"
                    value={formData.medicalInfo}
                    onChange={(e) => handleInputChange('medicalInfo', e.target.value)}
                    placeholder="Any medical conditions, allergies, or special requirements..."
                    rows={4}
                  />
                </div>
              </div>
            </Card>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Button 
              type="button" 
              variant="ghost"
              onClick={() => router.push('/students')}
            >
              Cancel
            </Button>
            <Button type="submit" variant="primary" disabled={loading}>
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Create Student
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}