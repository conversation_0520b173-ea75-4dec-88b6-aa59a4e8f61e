---
title: User & Role Management
status: ✅ COMPLETED
epic: 1
story: 1
last_updated: 2024-01-20
architecture_version: 2.0
---

# User Story: As a Tenant Admin, I want to create and manage user accounts for students, teachers, and staff within my school, so that I can control access to the system and maintain accurate records.

## Acceptance Criteria:
- The system shall allow a Tenant Admin to create new user accounts with specified roles (Student, Teacher, Staff).
- The system shall allow a Tenant Admin to edit existing user profiles and assign/reassign roles.
- The system shall ensure that each user account has a unique identifier within the tenant.
- The system shall prevent unauthorized access to tenant-specific data based on assigned roles.
- **The system shall provide dedicated dashboard panels for each user role with role-specific functionality and restrictions.**
- **Students shall only access student-specific features and cannot manage users, create courses, or access administrative functions.**
- **Teachers shall access teacher-specific features including academic management but cannot manage users or access tenant administration.**
- **Tenant <PERSON><PERSON> shall have full access to user management, role assignment, and tenant-specific administrative functions.**
- **Super Admins shall have system-wide access across all tenants with global administrative capabilities.**

## 🚨 **ARCHITECTURE UPDATE - COMPLETED IMPLEMENTATION**

### **✅ IMPLEMENTED COMPONENTS:**
- **SuperAdminDashboard.tsx** - System-wide tenant and user management
- **TenantAdminDashboard.tsx** - School-specific user management  
- **tenants/create/page.tsx** - Complete tenant creation with admin user setup
- **Sidebar.tsx** - Role-specific navigation menus
- **auditLogger.ts** - Comprehensive audit logging service
- **rolePermissions.ts** - Centralized role and permission definitions

### **✅ MULTI-TENANT HIERARCHY IMPLEMENTED:**
```
Super Admin (System Level)
    ↓ Creates Tenants (Schools) + Tenant Admin Users
Tenant Admin (School Level)  
    ↓ Creates School Users (Teachers, Students, Staff)
School Users (Within School)
    ↓ Access School-Specific Data Only
```

### **✅ ROLE-BASED DASHBOARDS:**
- **Super Admin**: System-wide tenant management, system audit, NECTA compliance
- **Tenant Admin**: School user management, academic programs, school reports
- **Teacher**: Academic functions within assigned school
- **Student**: Student portal access within their school

### **✅ USER CREATION FLOW:**
1. **Super Admin** → Creates **Tenants** (Schools) + **Tenant Admin** users via `/tenants/create`
2. **Tenant Admin** → Creates **School Users** (Teachers, Students, Staff) via `/users`
3. **No cross-tenant user creation** allowed

### **✅ AUDIT LOGGING:**
- All user creation, modification, and deletion actions logged
- Role assignment changes tracked
- Cross-tenant access attempts monitored
- Comprehensive audit trail for compliance

## Dev Notes:

### Previous Story Insights:
This story has been fully implemented with proper multi-tenant architecture, role-based access control, and comprehensive audit logging.

### Data Models:
-   **Users Table:** Authentication and authorization details (linked to Students/Teachers/Admins) will be managed in a `Users` table within PostgreSQL. Data will be normalized, relationships established with foreign keys, and appropriate indexing applied.

### API Specifications:
-   **RESTful APIs:** Primary method for external systems and frontend applications to interact with the backend services. All API endpoints will be secured using industry-standard authentication (e.g., OAuth2, JWT) and authorization (e.g., RBAC) mechanisms.

### Component Specifications:
-   **Frontend:** React components will be developed for user account creation, editing, and role management.
-   **Backend:** Node.js services will handle user account logic, interacting with the PostgreSQL database.

### File Locations:
-   Frontend components will reside in the `frontend/src/components` directory (or similar, based on project structure).
-   Backend API routes and controllers will be in `backend/routes` and `backend/controllers` respectively.
-   Database models/schemas will be defined in `backend/models`.

### Testing Requirements:
-   Unit tests for backend API endpoints (e.g., user creation, update, role assignment).
-   Integration tests to ensure proper interaction between frontend and backend.
-   End-to-end tests for user account management flows.
-   Security tests for authentication and authorization mechanisms.

### Technical Constraints:
-   **Frontend:** React
-   **Backend:** Node.js
-   **Database:** PostgreSQL
-   **Authentication:** OAuth2/JWT
-   **Authorization:** Role-Based Access Control (RBAC)

## Role-Based Access Control Specifications:

### Super Admin Role:
- **Dashboard Access**: System-wide dashboard with global metrics and tenant management
- **Permissions**: 
  - Manage all tenants and their configurations
  - Create/edit/delete tenant administrators
  - Access system-wide reports and analytics
  - Configure global system settings
  - View audit logs across all tenants
- **Restrictions**: Cannot directly manage individual students/teachers (delegated to tenant admins)

### Tenant Admin Role:
- **Dashboard Access**: Tenant-specific administrative dashboard
- **Permissions**:
  - Manage users within their tenant (students, teachers, staff)
  - Create/edit/delete user accounts and assign roles
  - Configure tenant-specific settings
  - Access tenant-specific reports and analytics
  - Manage academic structure (courses, subjects)
  - View tenant audit logs
- **Restrictions**: Cannot access other tenants' data or global system settings

### Teacher Role:
- **Dashboard Access**: Teacher-specific dashboard with academic focus
- **Permissions**:
  - View assigned students and classes
  - Access academic management (courses, subjects) - read only
  - Manage gradebooks and assessments
  - View student attendance and performance
  - Access teacher-specific reports
- **Restrictions**: Cannot manage users, create courses/subjects, or access administrative functions

### Student Role:
- **Dashboard Access**: Student-specific dashboard with limited functionality
- **Permissions**:
  - View personal academic records
  - Access assigned courses and subjects
  - View grades and attendance
  - Access student-specific announcements
- **Restrictions**: Cannot manage users, access administrative functions, or view other students' data

## Tasks / Subtasks:

- [x] Task 1 (AC: 1): Implement user account creation with role assignment.
- [x] Task 2 (AC: 2): Implement user profile editing and role reassignment.
- [x] Task 3 (AC: 3): Ensure unique user identifiers within the tenant.
- [x] Task 4 (AC: 4): Implement role-based access control for tenant-specific data.
- [x] Task 5 (AC: 5): Create dedicated dashboard components for each user role.
- [x] Task 6 (AC: 6): Implement frontend route protection based on user roles.
- [x] Task 7 (AC: 7): Implement backend API authorization middleware.
- [ ] Task 8 (AC: 8): Create role-specific navigation menus and UI restrictions.
- [ ] Task 9 (AC: 9): Implement comprehensive audit logging for role-based actions.