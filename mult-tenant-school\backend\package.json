{"name": "school-management-backend", "version": "1.0.0", "description": "Backend API for Multi-Tenant School Management System", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "db:reset": "prisma migrate reset --force && prisma db seed"}, "keywords": ["school", "management", "multi-tenant", "api"], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.16.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^17.2.2", "express": "^5.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "node-fetch": "^3.3.2", "prisma": "^6.16.1", "sqlite3": "^5.1.7"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.3.1", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.0.0"}, "prisma": {"seed": "node prisma/seed.js"}}